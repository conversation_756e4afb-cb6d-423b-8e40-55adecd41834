<?php

declare(strict_types=1);

namespace Comave\SellerApi\Model;

use Comave\SellerApi\Api\IntegrationInterface;
use Comave\SellerApi\Api\IntegrationInterfaceFactory;
use Comave\SellerApi\Exception\UnknownIntegrationException;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Event\ManagerInterface;
use Psr\Log\LoggerInterface;

class IntegrationTypePool
{
    public const string NON_INTEGRATED = 'non_integrated';

    private array $productIntegrations = [];
    private array $sellerIntegrations = [];

    /**
     * @param ManagerInterface $eventManager
     * @param LoggerInterface $logger
     * @param ResourceConnection $resourceConnection
     * @param ResourceConnection $resourceConnection
     * @param IntegrationInterface[] $integrationPool
     */
    public function __construct(
        private readonly ManagerInterface $eventManager,
        private readonly LoggerInterface $logger,
        private readonly IntegrationInterfaceFactory $integrationFactory,
        private readonly ResourceConnection $resourceConnection,
        private readonly array $integrationPool = []
    ) {
    }

    /**
     * @param string $sellerId
     * @return IntegrationInterface
     * @throws UnknownIntegrationException
     */
    public function identifyBySeller(string $sellerId): IntegrationInterface
    {
        if (empty($this->sellerIntegrations[$sellerId])) {
            $connection = $this->resourceConnection->getConnection();
            $integrationSelect = $connection->select()
                ->from(
                    $connection->getTableName(IntegrationInterface::TABLE_MARKETPLACE_PRODUCTS_FLAT),
                    ['integration_type']
                )->where(
                    'seller_id = ?',
                    $sellerId
                );

            $integrationType = $connection->fetchOne($integrationSelect) ?: false;

            if (empty($integrationType) || !isset($this->integrationPool[$integrationType])) {
                throw new UnknownIntegrationException(__('Could not identify seller integration'));
            }

            $this->sellerIntegrations[$sellerId] = $this->integrationPool[$integrationType];
        }

        $this->sellerIntegrations[$sellerId]->setSellerId($sellerId);

        return $this->sellerIntegrations[$sellerId];
    }

    /**
     * @param string $productId
     * @return IntegrationInterface
     * @throws UnknownIntegrationException
     */
    public function identifyIntegration(string $productId): IntegrationInterface
    {
        $nonIntegrated = self::NON_INTEGRATED;

        if (empty($this->productIntegrations[$productId])) {
            $connection = $this->resourceConnection->getConnection();
            $integrationSelect = $connection->select()
                ->from(
                    ['main_table' => $connection->getTableName('marketplace_product')],
                    [
                        'product_id' => new \Zend_Db_Expr(
                            "COALESCE(mpf.product_id, main_table.mageproduct_id)"
                        ),
                        'integration_type' => new \Zend_Db_Expr(
                            "COALESCE(mpf.integration_type, '$nonIntegrated')"
                        ),
                        'seller_id' => new \Zend_Db_Expr(
                            'COALESCE(mpf.seller_id, main_table.seller_id)'
                        )
                    ]
                )->joinLeft(
                    ['mpf' => $connection->getTableName(IntegrationInterface::TABLE_MARKETPLACE_PRODUCTS_FLAT)],
                    'mpf.product_id = main_table.mageproduct_id',
                    []
                )->where(
                    'main_table.mageproduct_id = ?',
                    $productId
                );

            $integrationProduct = $connection->fetchAssoc($integrationSelect) ?: [];
            if (empty($integrationProduct) || !isset($integrationProduct[$productId])) {
                throw new UnknownIntegrationException(__('Could not identify product integration'));
            }

            $this->productIntegrations = $integrationProduct;
        }

        $integrationName = $this->productIntegrations[$productId]['integration_type'] ?? false;

        if ($integrationName === self::NON_INTEGRATED) {
            $dispatchedIntegration = $this->integrationFactory->create([
                'integrationType' => self::NON_INTEGRATED,
            ]);

            $dispatchedIntegration->setSellerId(
                $this->productIntegrations[$productId]['seller_id']
            );

            return $dispatchedIntegration;
        }

        if (
            $integrationName === false ||
            !isset($this->integrationPool[$integrationName]) ||
            !$this->integrationPool[$integrationName] instanceof IntegrationInterface
        ) {
            throw new UnknownIntegrationException(__('Integration %1 does not exist', $integrationName ?: 'N/A'));
        }

        $dispatchedIntegration = $this->integrationPool[$integrationName];
        $dispatchedIntegration->setSellerId(
            $this->productIntegrations[$productId]['seller_id']
        );
        $this->eventManager->dispatch(
            'product_integration_assign',
            [
                'integration' => $dispatchedIntegration
            ]
        );

        return $dispatchedIntegration;
    }

    /**
     * @param string|null $integrationType
     * @return void
     */
    public function populate(?string $integrationType = null): void
    {
        $connection = $this->resourceConnection->getConnection();

        foreach ($this->integrationPool as $integration) {
            if (!$integration instanceof IntegrationInterface) {
                continue;
            }

            if ($integration->getIntegrationType() === self::NON_INTEGRATED) {
                continue;
            }
            
            if ($integrationType !== null && $integration->getIntegrationType() !== $integrationType) {
                continue;
            }

            $this->logger->info(
                '[ComaveMarketPlace] Mapping Product Data',
                [
                    'integration' => $integration->getIntegrationType()
                ]
            );

            $columns = [
                'seller_id' => $integration->getSellerColumnIdentifier(),
                'product_id' => $integration->getSellerColumnProduct(),
            ];

            if ($integration->getTableLink() === IntegrationInterface::TABLE_MARKETPLACE_PRODUCTS_FLAT) {
                $columns[] = 'integration_type';
            } else {
                $columns['integration_type'] = new \Zend_Db_Expr("'{$integration->getIntegrationType()}'");
            }

            $selectArr = $connection->select()
                ->from(
                    $connection->getTableName($integration->getTableLink()),
                    $columns
                )->where(
                    'seller_id > ?',
                    0
                );

            $productArr = $connection->fetchAll($selectArr);

            if (empty($productArr)) {
                $this->logger->info(
                    '[ComaveMarketPlace] No products found to map',
                    [
                        'integration' => $integration->getIntegrationType()
                    ]
                );

                continue;
            }

            try {
                $connection->insertOnDuplicate(
                    $connection->getTableName(IntegrationInterface::TABLE_MARKETPLACE_PRODUCTS_FLAT),
                    $productArr,
                    [
                        'integration_type'
                    ]
                );

                $this->logger->info(
                    '[ComaveMarketPlace] Finished mapping integration',
                    [
                        'integration' => $integration->getIntegrationType()
                    ]
                );
            } catch (\Exception $e) {
                $this->logger->info(
                    '[ComaveMarketPlace] Error mapping integration',
                    [
                        'integration' => $integration->getIntegrationType(),
                        'message' => $e->getMessage()
                    ]
                );

                continue;
            }
        }
    }
}
