<?php
declare(strict_types=1);

namespace Comave\FreeShippingTreshold\Controller\Seller;

use Magento\Framework\App\ActionInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Data\Form\FormKey\Validator as FormKeyValidator;
use Magento\Customer\Model\Session;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\App\RequestInterface;
use Comave\FreeShippingTreshold\Api\TresholdRepositoryInterface;
use Comave\FreeShippingTreshold\Model\TresholdFactory;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

/**
 * Handles saving and updating seller free shipping thresholds
 */
class Save implements ActionInterface
{
    protected RedirectFactory $resultRedirectFactory;
    protected FormKeyValidator $formKeyValidator;
    protected Session $customerSession;
    protected ManagerInterface $messageManager;
    protected RequestInterface $request;
    protected TresholdRepositoryInterface $repository;
    protected TresholdFactory $tresholdFactory;
    protected ResourceConnection $resource;
    protected LoggerInterface $logger;

    /**
     * Constructor
     */
    public function __construct(
        RedirectFactory $resultRedirectFactory,
        FormKeyValidator $formKeyValidator,
        Session $customerSession,
        ManagerInterface $messageManager,
        RequestInterface $request,
        TresholdRepositoryInterface $repository,
        TresholdFactory $tresholdFactory,
        ResourceConnection $resource,
        LoggerInterface $logger
    ) {
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->formKeyValidator = $formKeyValidator;
        $this->customerSession = $customerSession;
        $this->messageManager = $messageManager;
        $this->request = $request;
        $this->repository = $repository;
        $this->tresholdFactory = $tresholdFactory;
        $this->resource = $resource;
        $this->logger = $logger;
    }

    /**
     * Execute save action
     */
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();

        if (!$this->formKeyValidator->validate($this->request)) {
            $this->messageManager->addErrorMessage(__('Invalid form key.'));
            return $resultRedirect->setPath('comave_freeshippingtreshold/seller/form');
        }

        $data = $this->request->getPostValue();
        $customerId = $this->customerSession->getCustomerId();

        if (!$customerId || empty($data)) {
            $this->messageManager->addErrorMessage(__('Unauthorized or empty submission.'));
            return $resultRedirect->setPath('comave_freeshippingtreshold/seller/form');
        }

        try {
            $connection = $this->resource->getConnection();
            $sellerId = (int) $connection->fetchOne(
                $connection->select()
                    ->from('marketplace_userdata', 'entity_id')
                    ->where('seller_id = ?', $customerId)
            );

            if (!$sellerId) {
                throw new \Exception('Seller ID not found for customer.');
            }

            if (!empty($data['update_id'])) {
                $ruleId = (int)$data['update_id'];
                $minAmount = $data['min_amount'][$ruleId] ?? null;

                if ($minAmount !== null) {
                    $model = $this->repository->getById($ruleId);
                    $model->setMinAmount((float)$minAmount);
                    $this->repository->save($model);
                    $this->messageManager->addSuccessMessage(__('Threshold updated.'));
                }
            }

        if (!empty($data['country_code']) && !empty($data['new_min_amount']) && is_numeric($data['new_min_amount'])) {
            $countryCode = strtoupper(trim($data['country_code']));
            $minAmount = (float)$data['new_min_amount'];
            $this->logger->debug('Type of sellerId: ' . gettype($sellerId));
            $existing = $this->repository->getBySellerAndCountry($sellerId, $countryCode);

            if ($existing && $existing->getId()) {
                $this->messageManager->addNoticeMessage(__('A rule already exists for this country.'));
            } else {
                $model = $this->tresholdFactory->create();
                $model->setSellerId($sellerId);
                $model->setCountryCode($countryCode);
                $model->setMinAmount($minAmount);
                $this->repository->save($model);

                $this->messageManager->addSuccessMessage(__('New threshold added.'));
            }
        }

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('An error occurred while saving.'));
            $this->logger->error('[Threshold Save Error] ' . $e->getMessage());
        }

        return $resultRedirect->setPath('comave_freeshippingtreshold/seller/form');
    }
}
