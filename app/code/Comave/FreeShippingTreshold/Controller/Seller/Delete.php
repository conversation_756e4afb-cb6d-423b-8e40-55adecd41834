<?php
namespace Comave\FreeShippingTreshold\Controller\Seller;

use Magento\Framework\App\ActionInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\App\RequestInterface;
use Comave\FreeShippingTreshold\Api\TresholdRepositoryInterface;
use Magento\Framework\Message\ManagerInterface;
use Psr\Log\LoggerInterface;

class Delete implements ActionInterface
{
    protected RedirectFactory $resultRedirectFactory;
    protected RequestInterface $request;
    protected TresholdRepositoryInterface $repository;
    protected ManagerInterface $messageManager;
    protected LoggerInterface $logger;

    public function __construct(
        RedirectFactory $resultRedirectFactory,
        RequestInterface $request,
        TresholdRepositoryInterface $repository,
        ManagerInterface $messageManager,
        LoggerInterface $logger
    ) {
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->request = $request;
        $this->repository = $repository;
        $this->messageManager = $messageManager;
        $this->logger = $logger;
    }

    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();
        $id = (int)$this->request->getParam('id');

        if (!$id) {
            $this->messageManager->addErrorMessage(__('Invalid ID.'));
            return $resultRedirect->setPath('comave_freeshippingtreshold/seller/form');
        }

        try {
            $model = $this->repository->getById($id);
            $this->repository->delete($model);
            $this->messageManager->addSuccessMessage(__('Threshold deleted.'));
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('Unable to delete threshold.'));
            $this->logger->error('[Threshold Delete Error] ' . $e->getMessage());
        }

        return $resultRedirect->setPath('comave_freeshippingtreshold/seller/form');
    }
}
