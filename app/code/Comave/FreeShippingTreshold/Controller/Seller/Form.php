<?php
namespace Comave\FreeShippingTreshold\Controller\Seller;

use Magento\Framework\App\ActionInterface;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\App\RequestInterface;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;

class Form implements ActionInterface
{
    protected $resultPageFactory;
    protected $resultRedirectFactory;
    protected $request;
    protected $marketplaceHelper;

    public function __construct(
        PageFactory $resultPageFactory,
        RedirectFactory $resultRedirectFactory,
        RequestInterface $request,
        MarketplaceHelper $marketplaceHelper
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->request = $request;
        $this->marketplaceHelper = $marketplaceHelper;
    }

    public function execute()
    {
        if (!$this->marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->request->isSecure()]
            );
        }

        $resultPage = $this->resultPageFactory->create();

        if ($this->marketplaceHelper->getIsSeparatePanel()) {
            $resultPage->addHandle('freeshippingthreshold_seller_form');
        }

        $resultPage->getConfig()->getTitle()->set(__('Manage Free Shipping Thresholds'));

        return $resultPage;
    }
}
