<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">

    <table name="comave_freeshipping_treshold" resource="default" engine="innodb" comment="Seller Free Shipping Tresholds">
        <column name="id" xsi:type="int" unsigned="true" nullable="false" identity="true" comment="ID"/>
        <column name="seller_id" xsi:type="int" nullable="false" comment="Seller ID"/>
        <column name="country_code" xsi:type="varchar" length="2" nullable="false" comment="Country Code"/>
        <column name="min_amount" xsi:type="decimal" precision="12" scale="2" nullable="false" default="0.00" comment="Minimum Amount for Free Shipping"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>

        <constraint xsi:type="unique" referenceId="UNQ_SELLER_COUNTRY">
            <column name="seller_id"/>
            <column name="country_code"/>
        </constraint>
    </table>

</schema>
