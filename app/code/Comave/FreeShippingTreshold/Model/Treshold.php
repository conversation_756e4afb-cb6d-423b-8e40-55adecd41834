<?php
namespace Comave\FreeShippingTreshold\Model;

use Magento\Framework\Model\AbstractModel;

/**
 * Model for seller free shipping threshold rule.
 */
class Treshold extends AbstractModel
{
    /**
     * Initialize resource model
     */
    protected function _construct()
    {
        $this->_init(\Comave\FreeShippingTreshold\Model\ResourceModel\Treshold::class);
    }

    /**
     * Get ID
     */
    public function getId()
    {
        return $this->getData('id');
    }

    /**
     * Get Seller ID
     */
    public function getSellerId()
    {
        return $this->getData('seller_id');
    }

    /**
     * Set Seller ID
     */
    public function setSellerId($sellerId)
    {
        return $this->setData('seller_id', $sellerId);
    }

    /**
     * Get Country Code
     */
    public function getCountryCode()
    {
        return $this->getData('country_code');
    }

    /**
     * Set Country Code
     */
    public function setCountryCode($countryCode)
    {
        return $this->setData('country_code', $countryCode);
    }

    /**
     * Get Minimum Amount
     */
    public function getMinAmount()
    {
        return $this->getData('min_amount');
    }

    /**
     * Set Minimum Amount
     */
    public function setMinAmount($amount)
    {
        return $this->setData('min_amount', $amount);
    }
}
