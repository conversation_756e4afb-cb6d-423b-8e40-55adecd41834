<?php
namespace Comave\FreeShippingTreshold\Model\ResourceModel\Treshold;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

/**
 * Collection class for seller free shipping thresholds
 */
class Collection extends AbstractCollection
{
    /**
     * Initialize collection model and resource model
     */
    protected function _construct()
    {
        $this->_init(
            \Comave\FreeShippingTreshold\Model\Treshold::class,
            \Comave\FreeShippingTreshold\Model\ResourceModel\Treshold::class
        );
    }
}
