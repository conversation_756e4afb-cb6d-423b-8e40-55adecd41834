<?php
namespace Comave\FreeShippingTreshold\Model;

use Comave\FreeShippingTreshold\Api\TresholdRepositoryInterface;
use Comave\FreeShippingTreshold\Model\TresholdFactory;
use Comave\FreeShippingTreshold\Model\ResourceModel\Treshold as ResourceTreshold;
use Comave\FreeShippingTreshold\Model\ResourceModel\Treshold\CollectionFactory as TresholdCollectionFactory;
use Magento\Framework\Exception\NoSuchEntityException;

class TresholdRepository implements TresholdRepositoryInterface
{
    protected $resource;
    protected $tresholdFactory;
    protected $collectionFactory;

    public function __construct(
        ResourceTreshold $resource,
        TresholdFactory $tresholdFactory,
        TresholdCollectionFactory $collectionFactory
    ) {
        $this->resource = $resource;
        $this->tresholdFactory = $tresholdFactory;
        $this->collectionFactory = $collectionFactory;
    }

    public function save(Treshold $treshold): Treshold
    {
        $this->resource->save($treshold);
        return $treshold;
    }

    public function getById(int $id): Treshold
    {
        $treshold = $this->tresholdFactory->create();
        $this->resource->load($treshold, $id);
        if (!$treshold->getId()) {
            throw new NoSuchEntityException(__('Treshold with ID %1 not found.', $id));
        }
        return $treshold;
    }

    public function getBySellerAndCountry($sellerId, string $countryCode): ?Treshold
    {
        $collection = $this->collectionFactory->create()
            ->addFieldToFilter('seller_id', $sellerId)
            ->addFieldToFilter('country_code', strtoupper($countryCode))
            ->setPageSize(1);

        return $collection->getFirstItem()->getId() ? $collection->getFirstItem() : null;
    }

    public function delete(Treshold $treshold): bool
    {
        $this->resource->delete($treshold);
        return true;
    }
}
