<?php

declare(strict_types=1);

namespace Comave\FreeShippingTreshold\Block\Seller;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Framework\Data\Form\FormKey;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Directory\Model\ResourceModel\Country\CollectionFactory as CountryCollectionFactory;
use Comave\FreeShippingTreshold\Model\ResourceModel\Treshold\CollectionFactory as TresholdCollectionFactory;

/**
 * Block class for Free Shipping Thresholds seller form
 */
class Form extends Template
{
    protected FormKey $formKey;
    protected Session $customerSession;
    protected ScopeConfigInterface $scopeConfig;
    protected CountryCollectionFactory $countryCollectionFactory;
    protected TresholdCollectionFactory $tresholdCollectionFactory;
    protected ResourceConnection $resource;

    public function __construct(
        Context $context,
        FormKey $formKey,
        Session $customerSession,
        ScopeConfigInterface $scopeConfig,
        CountryCollectionFactory $countryCollectionFactory,
        TresholdCollectionFactory $tresholdCollectionFactory,
        ResourceConnection $resource,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->formKey = $formKey;
        $this->customerSession = $customerSession;
        $this->scopeConfig = $scopeConfig;
        $this->countryCollectionFactory = $countryCollectionFactory;
        $this->tresholdCollectionFactory = $tresholdCollectionFactory;
        $this->resource = $resource;
    }

    /**
     * Get form key for CSRF protection
     */
    public function getFormKey(): string
    {
        return $this->formKey->getFormKey();
    }

    /**
     * Get form action URL
     */
    public function getFormAction(): string
    {
        return $this->getUrl('comave_freeshippingtreshold/seller/save');
    }

    /**
     * Get array of available countries with optional top priority countries
     *
     * @return array<int, array{value: string, label: string}>
     */
    public function getCountries(): array
    {
        try {
            $collection = $this->countryCollectionFactory->create()->loadByStore();

            $topDestinations = $this->getTopDestinations();
            if (!empty($topDestinations)) {
                $collection->setForegroundCountries($topDestinations);
            }

            return $collection->toOptionArray();
        } catch (\Exception $e) {
            file_put_contents(BP . '/var/log/freeshipping_country_error.log', $e->getMessage());
            return [];
        }
    }

    /**
     * Get comma-separated list of top destination countries from config
     */
    protected function getTopDestinations(): array
    {
        $destinations = $this->scopeConfig->getValue(
            'general/country/destinations',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );

        return !empty($destinations) ? explode(',', $destinations) : [];
    }

    /**
     * Get all free shipping threshold rules for the current seller
     *
     * @return \Comave\FreeShippingTreshold\Model\ResourceModel\Treshold\Collection|array
     */
    public function getRules()
    {
        $customerId = $this->customerSession->getCustomerId();
        if (!$customerId) {
            return [];
        }

        try {
            $sellerId = $this->resource->getConnection()->fetchOne(
                $this->resource->getConnection()->select()
                    ->from('marketplace_userdata', 'entity_id')
                    ->where('seller_id = ?', $customerId)
            );

            if (!$sellerId) {
                return [];
            }

            $collection = $this->tresholdCollectionFactory->create();
            $collection->addFieldToFilter('seller_id', $sellerId);
            return $collection;
        } catch (\Exception $e) {
            file_put_contents(BP . '/var/log/freeshipping_rules_error.log', $e->getMessage());
            return [];
        }
    }
    public function getDeleteUrl(int $id): string
    {
        return $this->getUrl('comave_freeshippingtreshold/seller/delete', ['id' => $id]);
    }
}
