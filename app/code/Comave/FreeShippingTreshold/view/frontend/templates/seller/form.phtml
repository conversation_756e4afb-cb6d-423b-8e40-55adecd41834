<?php /** @var \Comave\FreeShippingTreshold\Block\Seller\Form $block */ ?>

<h2><?= __('Free Shipping Thresholds') ?></h2>

<form method="post" action="<?= $block->getFormAction() ?>">
    <input type="hidden" name="form_key" value="<?= $block->getFormKey() ?>" />

    <table class="data-grid">
        <thead>
            <tr>
                <th><?= __('Country') ?></th>
                <th><?= __('Minimum Order Value (€)') ?></th>
                <th><?= __('Actions') ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $rules = $block->getRules(); ?>
            <?php if (!empty($rules)): ?>
                <?php foreach ($rules as $rule): ?>
                    <tr>
                        <td><?= $rule->getCountryCode() ?></td>
                        <td>
                            <input type="number"
                                   step="0.01"
                                   name="min_amount[<?= $rule->getId() ?>]"
                                   value="<?= $rule->getMinAmount() ?>"
                                   class="input-text admin__control-text"
                                   required />
                        </td>
                        <td style="white-space: nowrap; text-align: center;">
                            <div class="actions-group" style="display: flex; gap: 8px;">
                                <button type="submit"
                                        name="update_id"
                                        value="<?= $rule->getId() ?>"
                                        class="action scalable save wk-ui-grid-btn wk-ui-grid-btn-primary"
                                        style="padding: 4px 10px;">
                                    <?= __('Save') ?>
                                </button>
                                <a href="<?= $block->getDeleteUrl($rule->getId()) ?>"
                                   onclick="return confirm('<?= __('Are you sure you want to delete this threshold?') ?>');"
                                   class="action-delete wk-ui-grid-btn"
                                   style="padding: 4px 10px; background-color: #f56c6c; color: white; text-decoration: none;">
                                    <?= __('Delete') ?>
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr><td colspan="3"><?= __('No rules found.') ?></td></tr>
            <?php endif; ?>
        </tbody>
    </table>
</form>

<h3><?= __('Add New Threshold') ?></h3>

<form method="post"
      action="<?= $block->getFormAction() ?>"
      data-mage-init='{"validation":{}}'
      enctype="multipart/form-data"
      class="wk-ui-component-container">

    <input type="hidden" name="form_key" value="<?= $block->getFormKey() ?>" />

    <div class="fieldset">
        <div class="field required" style="width: 35%; float:left; margin-right: 2%;">
            <label class="label" for="country_code">
                <span><?= __('Country') ?></span>
            </label>
            <div class="control">
                <select name="country_code"
                        id="country_code"
                        class="admin__control-select required-entry"
                        data-validate="{required:true}">
                    <option value=""><?= __('-- Select Country --') ?></option>
                    <?php foreach ($block->getCountries() as $country): ?>
                        <option value="<?= $country['value'] ?>"><?= $country['label'] ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>

        <div class="field required" style="width: 35%; float:left; margin-right: 2%;">
            <label class="label" for="new_min_amount">
                <span><?= __('Minimum Order Amount (€)') ?></span>
            </label>
            <div class="control">
                <input type="number"
                       name="new_min_amount"
                       id="new_min_amount"
                       step="0.01"
                       class="input-text admin__control-text required-entry"
                       data-validate="{required:true}"
                       placeholder="<?= __('e.g. 50.00') ?>"
                       required />
            </div>
        </div>

        <div class="field" style="width: 20%; float:left;">
            <label class="label" style="visibility: hidden;">
                <span><?= __('Add') ?></span>
            </label>
            <div class="control">
                <button type="submit"
                        title="<?= __('Add') ?>"
                        class="action scalable save primary wk-ui-grid-btn wk-ui-grid-btn-primary">
                    <span><?= __('Add') ?></span>
                </button>
            </div>
        </div>
    </div>
</form>

<div style="clear: both;"></div>
