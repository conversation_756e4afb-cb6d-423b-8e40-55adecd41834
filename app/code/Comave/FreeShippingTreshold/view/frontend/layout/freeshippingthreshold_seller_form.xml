<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>

        <css src="Webkul_Marketplace::css/wk_block.css"/>
        <css src="Webkul_Marketplace::css/style.css"/>
        <css src="Webkul_Marketplace::css/product.css"/>
        <css src="Webkul_Marketplace::css/layout.css"/>

    </head>
    <body>
        <referenceBlock name="seller.page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Manage FreeShipping Treshold</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="seller.content">
                <block class="Comave\FreeShippingTreshold\Block\Seller\Form" name="freeshipping_treshold_manage" template="Comave_FreeShippingTreshold::seller/form.phtml" cacheable="false" />
        </referenceContainer>
    </body>
</page>
