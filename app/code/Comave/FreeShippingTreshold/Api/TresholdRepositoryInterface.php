<?php
namespace Comave\FreeShippingTreshold\Api;

use Comave\FreeShippingTreshold\Model\Treshold;

interface TresholdRepositoryInterface
{
    /**
     * Save a Treshold entry
     *
     * @param Treshold $treshold
     * @return Treshold
     */
    public function save(Treshold $treshold): Treshold;

    /**
     * Get Treshold by ID
     *
     * @param int $id
     * @return Treshold
     */
    public function getById(int $id): Treshold;

    /**
     * Get Treshold by Seller ID and Country Code
     *
     * @param $sellerId
     * @param string $countryCode
     * @return Treshold|null
     */
    public function getBySellerAndCountry($sellerId, string $countryCode): ?Treshold;

    /**
     * Delete a Treshold entry
     *
     * @param Treshold $treshold
     * @return bool
     */
    public function delete(Treshold $treshold): bool;
}
